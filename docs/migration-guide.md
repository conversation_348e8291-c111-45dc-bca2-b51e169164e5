# Migration Guide: Xcode MCP Server v2.0

This guide helps you understand the major improvements and changes in the enhanced Xcode MCP Server.

## 🚀 What's New

### Major Improvements

1. **Security Enhancements**
   - Command injection prevention
   - Secure path validation
   - Input sanitization
   - Error context isolation

2. **Performance Optimizations**
   - Intelligent caching system
   - Performance monitoring
   - Parallel processing capabilities
   - Resource management

3. **Architecture Modernization**
   - Dependency injection container
   - Service lifetime management
   - Standardized tool patterns
   - Enhanced error handling

## 🔧 For Tool Developers

### Migrating from execAsync to SecureCommandExecutor

**Before:**
```typescript
import { promisify } from "util";
import { exec } from "child_process";
const execAsync = promisify(exec);

// Unsafe - vulnerable to command injection
const { stdout } = await execAsync(`ls -la "${userPath}"`);
```

**After:**
```typescript
// Secure - uses parameter arrays
const result = await server.commandExecutor.execute('ls', ['-la', userPath], {
  timeout: 5000,
  retries: 1
});
const stdout = result.stdout;
```

### Using the New Tool Factory

**Before:**
```typescript
server.server.tool(
  "my_tool",
  "Description",
  { param: z.string() },
  async ({ param }) => {
    // Manual error handling
    try {
      // Tool logic
      return { content: [{ type: "text", text: "result" }] };
    } catch (error) {
      throw new Error(`Tool failed: ${error.message}`);
    }
  }
);
```

**After:**
```typescript
import { ToolFactory, CommonSchemas, ToolCategories } from "../utils/toolFactory.js";

ToolFactory.registerTool(
  server,
  "my_tool",
  "Description",
  z.object({
    param: CommonSchemas.filePath
  }),
  async (params, server) => {
    // Tool logic with automatic error handling
    return ToolFactory.createSuccessResult("Operation completed", result);
  },
  {
    requiresProject: true,
    timeout: 10000,
    cache: { enabled: true, ttl: 60000 }
  },
  {
    category: ToolCategories.FILE,
    description: "Detailed description",
    examples: ["Example usage"]
  }
);
```

### Performance Monitoring

**Add performance tracking to expensive operations:**

```typescript
import { measureAsync, timed } from "../utils/performance.js";

// Method 1: Manual measurement
const { result, duration } = await measureAsync(
  'expensive_operation',
  async () => {
    // Your expensive operation
    return await someExpensiveTask();
  },
  { metadata: { param1: value1 } }
);

// Method 2: Decorator (for class methods)
class MyService {
  @timed('my_service.process')
  async process(data: any) {
    // Method automatically timed
    return processData(data);
  }
}
```

### Using the Caching System

```typescript
import { projectCache, CacheManager } from "../utils/cache.js";

// Use pre-configured cache
const cachedResult = await projectCache.getOrSet(
  'project_info_' + projectPath,
  async () => {
    return await getProjectInfo(projectPath);
  },
  300000 // 5 minutes TTL
);

// Create custom cache
const myCache = CacheManager.getCache('my_operations', {
  defaultTtl: 60000,
  maxSize: 100
});
```

## 🔒 Security Best Practices

### Path Validation

Always validate paths before file operations:

```typescript
// Validate for reading
const validPath = server.pathManager.validatePathForReading(userPath);

// Validate for writing
const validPath = server.pathManager.validatePathForWriting(userPath);

// Check if path is within allowed boundaries
if (!server.pathManager.isPathAllowed(userPath)) {
  throw new PathAccessError(userPath, "Path not allowed");
}
```

### Input Sanitization

Use the validation utilities:

```typescript
import { ValidationUtils } from "../utils/common.js";

// Validate email
if (!ValidationUtils.isEmail(email)) {
  throw new ValidationError('email', email, 'Invalid email format');
}

// Validate URL
if (!ValidationUtils.isUrl(url)) {
  throw new ValidationError('url', url, 'Invalid URL format');
}
```

### Error Handling

Use the enhanced error classes:

```typescript
import { 
  FileOperationError, 
  CommandExecutionError, 
  ValidationError 
} from "../utils/errors.js";

// File operation error with context
throw new FileOperationError(
  'read',
  filePath,
  originalError,
  { operation: 'backup', timestamp: Date.now() }
);

// Command execution error
throw new CommandExecutionError(
  'git status',
  stderr,
  exitCode,
  stdout,
  { repository: repoPath }
);
```

## 📊 Monitoring and Debugging

### Performance Reports

```typescript
import { PerformanceReporter, globalPerformanceMonitor } from "../utils/performance.js";

// Generate performance report
const report = PerformanceReporter.generateReport();
console.log(report);

// Log slow operations
PerformanceReporter.logSlowOperations(globalPerformanceMonitor, 1000);

// Get specific operation stats
const stats = globalPerformanceMonitor.getStats('project_detection');
if (stats) {
  console.log(`Average duration: ${stats.averageDuration}ms`);
}
```

### Cache Statistics

```typescript
import { CacheManager } from "../utils/cache.js";

// Get all cache statistics
const allStats = CacheManager.getAllStats();
console.log('Cache Statistics:', allStats);

// Get specific cache stats
const projectCacheStats = projectCache.getStats();
console.log(`Hit rate: ${(projectCacheStats.hitRate * 100).toFixed(2)}%`);
```

## 🔄 Breaking Changes

### Removed Dependencies

- `execAsync` from `child_process` - Use `SecureCommandExecutor` instead
- Direct `exec` calls - Use parameter arrays with `SecureCommandExecutor`

### Changed Interfaces

- Error constructors now accept optional context parameters
- Tool registration patterns have been standardized
- Service instances are now injected via dependency container

### New Requirements

- All external commands must use `SecureCommandExecutor`
- Path operations must use `PathManager` validation
- New tools should use `ToolFactory` for registration

## 🚀 Recommended Upgrade Path

1. **Update Command Execution**
   - Replace all `execAsync` calls with `SecureCommandExecutor`
   - Use parameter arrays instead of string concatenation

2. **Enhance Error Handling**
   - Update error throwing to use new error classes
   - Add context information to errors

3. **Add Performance Monitoring**
   - Identify expensive operations
   - Add timing to critical paths

4. **Implement Caching**
   - Cache expensive computations
   - Use appropriate TTL values

5. **Validate Security**
   - Ensure all paths are validated
   - Check input sanitization

## 📚 Additional Resources

- [Performance Monitoring Guide](./performance-monitoring.md)
- [Security Best Practices](./security-guide.md)
- [Tool Development Guide](./tool-development.md)
- [API Reference](./api-reference.md)

## 🆘 Getting Help

If you encounter issues during migration:

1. Check the error logs for detailed context information
2. Review the performance reports for bottlenecks
3. Validate that all security requirements are met
4. Consult the API documentation for new patterns

The enhanced server provides much better error messages and debugging information to help identify and resolve issues quickly.
