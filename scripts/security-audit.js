#!/usr/bin/env node

/**
 * Security audit script for Xcode MCP Server
 * Identifies potential security vulnerabilities in the codebase
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SecurityAuditor {
  constructor() {
    this.issues = [];
    this.srcDir = path.join(__dirname, "..", "src");
  }

  /**
   * Run the complete security audit
   */
  async audit() {
    console.log("🔍 Starting security audit...\n");

    await this.checkCommandInjection();
    await this.checkPathTraversal();
    await this.checkInputValidation();
    await this.checkErrorLeakage();

    this.generateReport();
  }

  /**
   * Check for command injection vulnerabilities
   */
  async checkCommandInjection() {
    console.log("🔒 Checking for command injection vulnerabilities...");

    const files = await this.getTypeScriptFiles();

    for (const file of files) {
      const content = fs.readFileSync(file, "utf8");
      const lines = content.split("\n");

      lines.forEach((line, index) => {
        // Check for template literals in command execution
        if (
          line.includes("execAsync") &&
          line.includes("`") &&
          line.includes("${")
        ) {
          this.addIssue(
            "CRITICAL",
            "Command Injection",
            file,
            index + 1,
            "Template literal with variable interpolation in execAsync call",
            line.trim()
          );
        }

        // Check for string concatenation in commands
        if (line.includes("execAsync") && line.includes("+")) {
          this.addIssue(
            "HIGH",
            "Command Injection",
            file,
            index + 1,
            "String concatenation in execAsync call",
            line.trim()
          );
        }

        // Check for unsafe shell commands
        if (
          line.includes("exec(") &&
          (line.includes("${") || line.includes("+"))
        ) {
          this.addIssue(
            "CRITICAL",
            "Command Injection",
            file,
            index + 1,
            "Unsafe shell command execution",
            line.trim()
          );
        }
      });
    }
  }

  /**
   * Check for path traversal vulnerabilities
   */
  async checkPathTraversal() {
    console.log("🛡️ Checking for path traversal vulnerabilities...");

    const files = await this.getTypeScriptFiles();

    for (const file of files) {
      const content = fs.readFileSync(file, "utf8");
      const lines = content.split("\n");

      lines.forEach((line, index) => {
        // Check for direct path operations without validation
        if (
          (line.includes("fs.") || line.includes("await fs")) &&
          !line.includes("validatePath") &&
          !line.includes("normalizePath") &&
          (line.includes("readFile") ||
            line.includes("writeFile") ||
            line.includes("unlink"))
        ) {
          this.addIssue(
            "MEDIUM",
            "Path Traversal",
            file,
            index + 1,
            "File operation without path validation",
            line.trim()
          );
        }

        // Check for path.join with user input
        if (
          (line.includes("path.join") && line.includes("params.")) ||
          line.includes("${")
        ) {
          this.addIssue(
            "MEDIUM",
            "Path Traversal",
            file,
            index + 1,
            "path.join with potentially unsafe user input",
            line.trim()
          );
        }
      });
    }
  }

  /**
   * Check for input validation issues
   */
  async checkInputValidation() {
    console.log("✅ Checking for input validation issues...");

    const files = await this.getTypeScriptFiles();

    for (const file of files) {
      const content = fs.readFileSync(file, "utf8");
      const lines = content.split("\n");

      lines.forEach((line, index) => {
        // Check for missing input sanitization
        if (line.includes("server.server.tool") && !content.includes("z.")) {
          this.addIssue(
            "LOW",
            "Input Validation",
            file,
            index + 1,
            "Tool registration without Zod schema validation",
            line.trim()
          );
        }

        // Check for direct use of user input in sensitive operations
        if (
          (line.includes("params.") || line.includes("args.")) &&
          (line.includes("exec") || line.includes("spawn"))
        ) {
          this.addIssue(
            "HIGH",
            "Input Validation",
            file,
            index + 1,
            "Direct use of user input in command execution",
            line.trim()
          );
        }
      });
    }
  }

  /**
   * Check for information leakage in error messages
   */
  async checkErrorLeakage() {
    console.log("🔐 Checking for information leakage...");

    const files = await this.getTypeScriptFiles();

    for (const file of files) {
      const content = fs.readFileSync(file, "utf8");
      const lines = content.split("\n");

      lines.forEach((line, index) => {
        // Check for stack traces in error messages
        if (line.includes("error.stack") && !line.includes("// DEBUG")) {
          this.addIssue(
            "MEDIUM",
            "Information Leakage",
            file,
            index + 1,
            "Stack trace potentially exposed in error message",
            line.trim()
          );
        }

        // Check for system paths in error messages
        if (
          line.includes("throw new Error") &&
          line.includes("${") &&
          (line.includes("path") || line.includes("dir"))
        ) {
          this.addIssue(
            "LOW",
            "Information Leakage",
            file,
            index + 1,
            "System path potentially exposed in error message",
            line.trim()
          );
        }
      });
    }
  }

  /**
   * Get all TypeScript files in the src directory
   */
  async getTypeScriptFiles() {
    const files = [];

    const scanDir = (dir) => {
      const entries = fs.readdirSync(dir);

      for (const entry of entries) {
        const fullPath = path.join(dir, entry);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          scanDir(fullPath);
        } else if (entry.endsWith(".ts")) {
          files.push(fullPath);
        }
      }
    };

    scanDir(this.srcDir);
    return files;
  }

  /**
   * Add a security issue to the report
   */
  addIssue(severity, category, file, line, description, code) {
    this.issues.push({
      severity,
      category,
      file: path.relative(process.cwd(), file),
      line,
      description,
      code,
    });
  }

  /**
   * Generate and display the security audit report
   */
  generateReport() {
    console.log("\n📊 Security Audit Report");
    console.log("========================\n");

    if (this.issues.length === 0) {
      console.log("✅ No security issues found!");
      return;
    }

    // Group issues by severity
    const bySeverity = this.issues.reduce((acc, issue) => {
      if (!acc[issue.severity]) acc[issue.severity] = [];
      acc[issue.severity].push(issue);
      return acc;
    }, {});

    // Display summary
    console.log("Summary:");
    console.log(`  🔴 Critical: ${(bySeverity.CRITICAL || []).length}`);
    console.log(`  🟠 High: ${(bySeverity.HIGH || []).length}`);
    console.log(`  🟡 Medium: ${(bySeverity.MEDIUM || []).length}`);
    console.log(`  🔵 Low: ${(bySeverity.LOW || []).length}`);
    console.log(`  📊 Total: ${this.issues.length}\n`);

    // Display issues by severity
    const severityOrder = ["CRITICAL", "HIGH", "MEDIUM", "LOW"];
    const severityIcons = {
      CRITICAL: "🔴",
      HIGH: "🟠",
      MEDIUM: "🟡",
      LOW: "🔵",
    };

    for (const severity of severityOrder) {
      const issues = bySeverity[severity];
      if (!issues || issues.length === 0) continue;

      console.log(`${severityIcons[severity]} ${severity} Issues:`);
      console.log("─".repeat(50));

      issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue.category}: ${issue.description}`);
        console.log(`   File: ${issue.file}:${issue.line}`);
        console.log(`   Code: ${issue.code}`);
        console.log("");
      });
    }

    // Recommendations
    console.log("🔧 Recommendations:");
    console.log("─".repeat(50));

    if (bySeverity.CRITICAL || bySeverity.HIGH) {
      console.log(
        "1. 🚨 Address CRITICAL and HIGH severity issues immediately"
      );
      console.log(
        "2. 🔒 Replace all execAsync calls with SecureCommandExecutor"
      );
      console.log("3. 🛡️ Add input validation and sanitization");
    }

    if (bySeverity.MEDIUM) {
      console.log(
        "4. 📋 Review MEDIUM severity issues for potential improvements"
      );
      console.log("5. 🔍 Add path validation to all file operations");
    }

    if (bySeverity.LOW) {
      console.log(
        "6. 📝 Consider addressing LOW severity issues for best practices"
      );
    }

    console.log(
      "\n✨ Run this audit regularly to maintain security standards!"
    );
  }
}

// Run the audit
const auditor = new SecurityAuditor();
auditor.audit().catch(console.error);
