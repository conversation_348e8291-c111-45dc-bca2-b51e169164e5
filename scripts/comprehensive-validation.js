#!/usr/bin/env node

/**
 * Comprehensive Validation Script
 * Tests all new utility classes and validates production readiness
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ComprehensiveValidator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: [],
    };
  }

  log(message, type = "info") {
    const timestamp = new Date().toISOString();
    const prefix =
      {
        info: "📋",
        success: "✅",
        warning: "⚠️",
        error: "❌",
      }[type] || "📋";

    console.log(`${prefix} [${timestamp}] ${message}`);

    this.results.details.push({
      timestamp,
      type,
      message,
    });
  }

  async validateFileExists(filePath, description) {
    try {
      await fs.promises.access(filePath);
      this.log(`${description} exists: ${filePath}`, "success");
      this.results.passed++;
      return true;
    } catch (error) {
      this.log(`${description} missing: ${filePath}`, "error");
      this.results.failed++;
      return false;
    }
  }

  async validateFileContent(filePath, patterns, description) {
    try {
      const content = await fs.promises.readFile(filePath, "utf-8");
      let allPatternsFound = true;

      for (const pattern of patterns) {
        if (typeof pattern === "string") {
          if (!content.includes(pattern)) {
            this.log(
              `Pattern "${pattern}" not found in ${description}`,
              "warning"
            );
            this.results.warnings++;
            allPatternsFound = false;
          }
        } else if (pattern instanceof RegExp) {
          if (!pattern.test(content)) {
            this.log(`Regex pattern not found in ${description}`, "warning");
            this.results.warnings++;
            allPatternsFound = false;
          }
        }
      }

      if (allPatternsFound) {
        this.log(`All patterns found in ${description}`, "success");
        this.results.passed++;
      }

      return allPatternsFound;
    } catch (error) {
      this.log(`Error reading ${description}: ${error.message}`, "error");
      this.results.failed++;
      return false;
    }
  }

  async validateNewUtilityClasses() {
    this.log("🔍 Validating New Utility Classes...", "info");

    const utilityClasses = [
      {
        file: "src/utils/securityUtils.ts",
        patterns: [
          "export class SecureErrorFormatter",
          "sanitizeErrorMessage",
          "createSecureError",
          "SENSITIVE_PATTERNS",
        ],
        description: "SecureErrorFormatter class",
      },
      {
        file: "src/utils/aiAgentEnhancer.ts",
        patterns: [
          "export class AIAgentEnhancer",
          "analyzeProject",
          "analyzeProjectStructure",
          "analyzeDependencies",
          "analyzeArchitecture",
        ],
        description: "AIAgentEnhancer class",
      },
      {
        file: "src/utils/healthMonitor.ts",
        patterns: [
          "export class HealthMonitor",
          "getInstance",
          "runAllChecks",
          "getHealthStatus",
          "getSystemMetrics",
        ],
        description: "HealthMonitor class",
      },
      {
        file: "src/utils/testFramework.ts",
        patterns: [
          "export class TestFramework",
          "runTestSuite",
          "registerTest",
          "testPathValidation",
          "testCacheEfficiency",
        ],
        description: "TestFramework class",
      },
    ];

    for (const utilityClass of utilityClasses) {
      await this.validateFileExists(
        utilityClass.file,
        utilityClass.description
      );
      await this.validateFileContent(
        utilityClass.file,
        utilityClass.patterns,
        utilityClass.description
      );
    }
  }

  async validateEnhancedPerformanceMonitoring() {
    this.log("⚡ Validating Enhanced Performance Monitoring...", "info");

    await this.validateFileContent(
      "src/utils/performanceOptimizer.ts",
      [
        "generatePerformanceRecommendations",
        "getCacheAnalysis",
        "getPerformanceStats",
        "systemMetrics",
      ],
      "Enhanced PerformanceOptimizer"
    );
  }

  async validateDocumentation() {
    this.log("📚 Validating Documentation...", "info");

    const documentationFiles = [
      {
        file: "PRODUCTION_READINESS_ENHANCEMENT_PLAN.md",
        patterns: [
          "Comprehensive Production Readiness Enhancement Plan",
          "Phase 1: Security Hardening",
          "Phase 2: Performance & Architecture Optimization",
          "Phase 3: AI Agent Development Enhancement",
          "Phase 4: Production Readiness",
        ],
        description: "Enhancement Plan Documentation",
      },
      {
        file: "COMPREHENSIVE_PRODUCTION_READINESS_REPORT.md",
        patterns: [
          "Comprehensive Production Readiness Audit",
          "Security Excellence",
          "Performance Excellence",
          "AI Agent Excellence",
          "Technical Implementation Details",
        ],
        description: "Comprehensive Report Documentation",
      },
    ];

    for (const doc of documentationFiles) {
      await this.validateFileExists(doc.file, doc.description);
      await this.validateFileContent(doc.file, doc.patterns, doc.description);
    }
  }

  async validateBuildArtifacts() {
    this.log("🔨 Validating Build Artifacts...", "info");

    const buildFiles = [
      "dist/index.js",
      "dist/server.js",
      "dist/utils/securityUtils.js",
      "dist/utils/aiAgentEnhancer.js",
      "dist/utils/healthMonitor.js",
      "dist/utils/testFramework.js",
    ];

    for (const buildFile of buildFiles) {
      await this.validateFileExists(buildFile, `Build artifact: ${buildFile}`);
    }
  }

  async validatePackageConfiguration() {
    this.log("📦 Validating Package Configuration...", "info");

    try {
      const packageJson = JSON.parse(
        await fs.promises.readFile("package.json", "utf-8")
      );

      // Check essential scripts
      const requiredScripts = ["build", "clean", "start"];
      for (const script of requiredScripts) {
        if (packageJson.scripts && packageJson.scripts[script]) {
          this.log(`Script "${script}" found in package.json`, "success");
          this.results.passed++;
        } else {
          this.log(`Script "${script}" missing in package.json`, "warning");
          this.results.warnings++;
        }
      }

      // Check dependencies
      const requiredDeps = ["@modelcontextprotocol/sdk", "zod", "dotenv"];
      for (const dep of requiredDeps) {
        if (packageJson.dependencies && packageJson.dependencies[dep]) {
          this.log(`Dependency "${dep}" found`, "success");
          this.results.passed++;
        } else {
          this.log(`Dependency "${dep}" missing`, "error");
          this.results.failed++;
        }
      }
    } catch (error) {
      this.log(`Error reading package.json: ${error.message}`, "error");
      this.results.failed++;
    }
  }

  async validateTypeScriptConfiguration() {
    this.log("🔧 Validating TypeScript Configuration...", "info");

    try {
      const tsConfig = JSON.parse(
        await fs.promises.readFile("tsconfig.json", "utf-8")
      );

      // Check strict mode
      if (tsConfig.compilerOptions && tsConfig.compilerOptions.strict) {
        this.log("TypeScript strict mode enabled", "success");
        this.results.passed++;
      } else {
        this.log("TypeScript strict mode not enabled", "warning");
        this.results.warnings++;
      }

      // Check module resolution
      if (
        tsConfig.compilerOptions &&
        tsConfig.compilerOptions.moduleResolution === "node"
      ) {
        this.log("Module resolution set to node", "success");
        this.results.passed++;
      }
    } catch (error) {
      this.log(`Error reading tsconfig.json: ${error.message}`, "error");
      this.results.failed++;
    }
  }

  async validateProjectStructure() {
    this.log("🏗️ Validating Project Structure...", "info");

    const requiredDirectories = [
      "src",
      "src/tools",
      "src/utils",
      "src/types",
      "scripts",
      "docs",
      "dist",
    ];

    for (const dir of requiredDirectories) {
      try {
        const stats = await fs.promises.stat(dir);
        if (stats.isDirectory()) {
          this.log(`Directory exists: ${dir}`, "success");
          this.results.passed++;
        } else {
          this.log(`Path exists but is not a directory: ${dir}`, "warning");
          this.results.warnings++;
        }
      } catch (error) {
        this.log(`Directory missing: ${dir}`, "error");
        this.results.failed++;
      }
    }
  }

  async validateSecurityImplementation() {
    this.log("🔒 Validating Security Implementation...", "info");

    // Check for security patterns in key files
    const securityPatterns = [
      "SecureErrorFormatter",
      "sanitizeErrorMessage",
      "validateFilePath",
      "PathAccessError",
    ];

    const keyFiles = [
      "src/utils/securityUtils.ts",
      "src/utils/pathManager.ts",
      "src/server.ts",
    ];

    for (const file of keyFiles) {
      const content = await fs.promises.readFile(file, "utf-8").catch(() => "");
      let securityPatternsFound = 0;

      for (const pattern of securityPatterns) {
        if (content.includes(pattern)) {
          securityPatternsFound++;
        }
      }

      if (securityPatternsFound > 0) {
        this.log(
          `Security patterns found in ${file} (${securityPatternsFound}/${securityPatterns.length})`,
          "success"
        );
        this.results.passed++;
      } else {
        this.log(`No security patterns found in ${file}`, "warning");
        this.results.warnings++;
      }
    }
  }

  generateReport() {
    this.log("📊 Generating Validation Report...", "info");

    const total =
      this.results.passed + this.results.failed + this.results.warnings;
    const successRate =
      total > 0 ? ((this.results.passed / total) * 100).toFixed(1) : 0;

    console.log("\n" + "=".repeat(80));
    console.log("🎯 COMPREHENSIVE VALIDATION REPORT");
    console.log("=".repeat(80));
    console.log(`📊 Total Checks: ${total}`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️  Warnings: ${this.results.warnings}`);
    console.log(`📈 Success Rate: ${successRate}%`);
    console.log("=".repeat(80));

    if (this.results.failed === 0) {
      console.log("🎉 ALL CRITICAL VALIDATIONS PASSED!");
      console.log("✅ The Xcode MCP Server is production-ready.");
    } else {
      console.log("❌ CRITICAL ISSUES FOUND!");
      console.log("🔧 Please address failed validations before deployment.");
    }

    if (this.results.warnings > 0) {
      console.log(
        `⚠️  ${this.results.warnings} warnings detected - consider addressing for optimal performance.`
      );
    }

    console.log("=".repeat(80));

    return {
      success: this.results.failed === 0,
      total,
      passed: this.results.passed,
      failed: this.results.failed,
      warnings: this.results.warnings,
      successRate: parseFloat(successRate),
    };
  }

  async run() {
    console.log("🚀 Starting Comprehensive Validation...\n");

    await this.validateProjectStructure();
    await this.validatePackageConfiguration();
    await this.validateTypeScriptConfiguration();
    await this.validateBuildArtifacts();
    await this.validateNewUtilityClasses();
    await this.validateEnhancedPerformanceMonitoring();
    await this.validateSecurityImplementation();
    await this.validateDocumentation();

    return this.generateReport();
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new ComprehensiveValidator();
  validator
    .run()
    .then((report) => {
      process.exit(report.success ? 0 : 1);
    })
    .catch((error) => {
      console.error("❌ Validation failed with error:", error);
      process.exit(1);
    });
}

export default ComprehensiveValidator;
