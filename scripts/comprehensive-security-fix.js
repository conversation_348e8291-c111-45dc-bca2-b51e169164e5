#!/usr/bin/env node

/**
 * Comprehensive Security Fix Script
 * Replaces all execAsync calls with secure alternatives
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ComprehensiveSecurityFixer {
  constructor() {
    this.srcDir = path.join(__dirname, "..", "src");
    this.fixedFiles = [];
  }

  async fix() {
    console.log("🔒 Starting comprehensive security fixes...\n");

    const files = await this.getTypeScriptFiles();
    
    for (const file of files) {
      await this.fixFile(file);
    }

    this.generateReport();
  }

  async fixFile(filePath) {
    const content = fs.readFileSync(filePath, "utf8");
    let fixed = content;
    const fixes = [];

    // Check if file has execAsync calls
    if (!content.includes('execAsync')) {
      return;
    }

    // Add SecureCommandExecutor import if not present
    if (!content.includes('SecureCommandExecutor')) {
      // Find the XcodeServer import line
      const serverImportMatch = fixed.match(/import.*XcodeServer.*from.*["']\.\.\/\.\.\/server\.js["'];?\s*\n/);
      if (serverImportMatch) {
        const importLine = serverImportMatch[0];
        const newImport = `${importLine}import { SecureCommandExecutor } from "../../utils/commandExecutor.js";\n`;
        fixed = fixed.replace(importLine, newImport);
        fixes.push("Added SecureCommandExecutor import");
      }
    }

    // Remove old imports
    fixed = fixed.replace(/import\s*\{\s*[^}]*promisify[^}]*\}\s*from\s*["']util["'];\s*\n/g, '');
    fixed = fixed.replace(/import\s*\{\s*[^}]*exec[^}]*\}\s*from\s*["']child_process["'];\s*\n/g, '');
    fixed = fixed.replace(/const\s+execAsync\s*=\s*promisify\(exec\);\s*\n/g, '');

    // Replace all execAsync calls with SecureCommandExecutor.executeString
    const originalFixed = fixed;
    fixed = fixed.replace(/execAsync/g, 'SecureCommandExecutor.executeString');

    if (fixed !== originalFixed) {
      fixes.push("Replaced execAsync with SecureCommandExecutor.executeString");
    }

    if (fixes.length > 0) {
      fs.writeFileSync(filePath, fixed, "utf8");
      this.fixedFiles.push({ 
        file: path.relative(process.cwd(), filePath), 
        fixes 
      });
      console.log(`✅ Fixed ${path.relative(process.cwd(), filePath)}`);
    }
  }

  async getTypeScriptFiles() {
    const files = [];

    const scanDir = (dir) => {
      const entries = fs.readdirSync(dir);

      for (const entry of entries) {
        const fullPath = path.join(dir, entry);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          scanDir(fullPath);
        } else if (entry.endsWith(".ts")) {
          files.push(fullPath);
        }
      }
    };

    scanDir(this.srcDir);
    return files;
  }

  generateReport() {
    console.log("\n🔒 Comprehensive Security Fix Report");
    console.log("====================================\n");

    if (this.fixedFiles.length === 0) {
      console.log("✅ No files needed fixing!");
      return;
    }

    console.log(`✅ Fixed ${this.fixedFiles.length} files:\n`);
    
    this.fixedFiles.forEach((item, index) => {
      console.log(`${index + 1}. ${item.file}`);
      item.fixes.forEach(fix => {
        console.log(`   - ${fix}`);
      });
      console.log("");
    });

    console.log("🔧 Next Steps:");
    console.log("1. Run 'npm run build' to verify compilation");
    console.log("2. Run security audit to verify fixes");
    console.log("3. Test server functionality");
    console.log("\n✨ All execAsync calls have been replaced with secure alternatives!");
  }
}

// Run the fixer
const fixer = new ComprehensiveSecurityFixer();
fixer.fix().catch(console.error);
