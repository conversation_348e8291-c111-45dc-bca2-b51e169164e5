/**
 * Performance optimization utilities
 * Provides caching, memoization, and performance monitoring enhancements
 */

import { TimeBasedCache, CacheManager } from "./cache.js";
import { globalPerformanceMonitor } from "./performance.js";

/**
 * Performance optimization configuration
 */
export interface OptimizationConfig {
  enableCaching?: boolean;
  enableMemoization?: boolean;
  enablePerformanceMonitoring?: boolean;
  cacheConfig?: {
    defaultTtl?: number;
    maxSize?: number;
  };
}

/**
 * Memoization decorator for expensive operations
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  keyGenerator?: (...args: Parameters<T>) => string,
  ttl = 300000 // 5 minutes default
): T {
  const cache = new Map<string, { value: ReturnType<T>; timestamp: number }>();

  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    const now = Date.now();
    const cached = cache.get(key);

    if (cached && now - cached.timestamp < ttl) {
      return cached.value;
    }

    const result = fn(...args);
    cache.set(key, { value: result, timestamp: now });

    // Clean up expired entries periodically
    if (cache.size > 100) {
      for (const [k, v] of cache.entries()) {
        if (now - v.timestamp >= ttl) {
          cache.delete(k);
        }
      }
    }

    return result;
  }) as T;
}

/**
 * Async memoization decorator
 */
export function memoizeAsync<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator?: (...args: Parameters<T>) => string,
  ttl = 300000 // 5 minutes default
): T {
  const cache = new Map<
    string,
    {
      promise: Promise<Awaited<ReturnType<T>>>;
      timestamp: number;
      resolved: boolean;
      value?: Awaited<ReturnType<T>>;
    }
  >();

  return (async (...args: Parameters<T>): Promise<Awaited<ReturnType<T>>> => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    const now = Date.now();
    const cached = cache.get(key);

    if (cached && now - cached.timestamp < ttl) {
      if (cached.resolved && cached.value !== undefined) {
        return cached.value;
      } else {
        return await cached.promise;
      }
    }

    const promise = fn(...args) as Promise<Awaited<ReturnType<T>>>;
    cache.set(key, { promise, timestamp: now, resolved: false });

    try {
      const result = await promise;
      cache.set(key, {
        promise,
        timestamp: now,
        resolved: true,
        value: result,
      });
      return result;
    } catch (error) {
      cache.delete(key); // Remove failed promises
      throw error;
    }
  }) as T;
}

/**
 * Performance-optimized file system operations
 */
export class OptimizedFileOperations {
  private static fileStatsCache = CacheManager.getCache("file_stats", {
    defaultTtl: 30000, // 30 seconds
    maxSize: 1000,
  });

  private static fileContentCache = CacheManager.getCache("file_content", {
    defaultTtl: 60000, // 1 minute
    maxSize: 100,
  });

  /**
   * Cached file stats check
   */
  static async getFileStats(filePath: string) {
    return this.fileStatsCache.getOrSet(filePath, async () => {
      const fs = await import("fs/promises");
      return fs.stat(filePath);
    });
  }

  /**
   * Cached file existence check
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await this.getFileStats(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Cached file content reading (for small files)
   */
  static async readFileContent(
    filePath: string,
    maxSize = 1024 * 1024
  ): Promise<string> {
    const stats = await this.getFileStats(filePath);

    if (stats.size > maxSize) {
      // Don't cache large files
      const fs = await import("fs/promises");
      return fs.readFile(filePath, "utf-8");
    }

    return this.fileContentCache.getOrSet(
      `${filePath}_${stats.mtime.getTime()}`,
      async () => {
        const fs = await import("fs/promises");
        return fs.readFile(filePath, "utf-8");
      }
    );
  }
}

/**
 * Batch operation optimizer
 */
export class BatchOptimizer {
  private static batches = new Map<
    string,
    {
      operations: Array<() => Promise<any>>;
      timeout: NodeJS.Timeout;
      resolve: (results: any[]) => void;
      reject: (error: Error) => void;
    }
  >();

  /**
   * Batch multiple operations together for efficiency
   */
  static async batchOperation<T>(
    batchKey: string,
    operation: () => Promise<T>,
    delay = 100
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      let batch = this.batches.get(batchKey);

      if (!batch) {
        const operations: Array<() => Promise<any>> = [];
        const promises: Array<{
          resolve: (value: any) => void;
          reject: (error: Error) => void;
        }> = [];

        batch = {
          operations,
          timeout: setTimeout(async () => {
            this.batches.delete(batchKey);

            try {
              const results = await Promise.allSettled(
                operations.map((op) => op())
              );

              results.forEach((result, index) => {
                if (result.status === "fulfilled") {
                  promises[index].resolve(result.value);
                } else {
                  promises[index].reject(result.reason);
                }
              });
            } catch (error) {
              promises.forEach((p) => p.reject(error as Error));
            }
          }, delay),
          resolve: () => {}, // Will be set below
          reject: () => {}, // Will be set below
        };

        this.batches.set(batchKey, batch);
      }

      batch.operations.push(operation);

      // Store the resolve/reject for this specific operation
      const operationIndex = batch.operations.length - 1;
      const operationPromise = { resolve, reject };

      // We need to track individual promises within the batch
      if (!(batch as any).promises) {
        (batch as any).promises = [];
      }
      (batch as any).promises[operationIndex] = operationPromise;
    });
  }
}

/**
 * Performance monitoring decorator
 */
export function performanceMonitor(operationName?: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const opName = operationName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      const timer = globalPerformanceMonitor.startTimer(opName);

      try {
        const result = await originalMethod.apply(this, args);
        timer.stop({ success: true });
        return result;
      } catch (error) {
        timer.stop({
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Debounce decorator for reducing excessive calls
 */
export function debounce(delay: number) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    let timeoutId: NodeJS.Timeout;

    descriptor.value = function (...args: any[]) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        originalMethod.apply(this, args);
      }, delay);
    };

    return descriptor;
  };
}

/**
 * Throttle decorator for rate limiting
 */
export function throttle(limit: number) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    let lastCall = 0;

    descriptor.value = function (...args: any[]) {
      const now = Date.now();
      if (now - lastCall >= limit) {
        lastCall = now;
        return originalMethod.apply(this, args);
      }
    };

    return descriptor;
  };
}

/**
 * Global performance optimizer
 */
export class PerformanceOptimizer {
  private static config: OptimizationConfig = {
    enableCaching: true,
    enableMemoization: true,
    enablePerformanceMonitoring: true,
    cacheConfig: {
      defaultTtl: 300000,
      maxSize: 1000,
    },
  };

  /**
   * Configure performance optimization settings
   */
  static configure(config: Partial<OptimizationConfig>) {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  static getConfig(): OptimizationConfig {
    return { ...this.config };
  }

  /**
   * Clear all caches
   */
  static clearAllCaches() {
    CacheManager.destroyAll();
  }

  /**
   * Get comprehensive performance statistics
   */
  static getPerformanceStats() {
    const cacheStats = CacheManager.getAllStats();
    const performanceStats = globalPerformanceMonitor.getAllStats();
    const slowOperations = globalPerformanceMonitor.getSlowOperations();

    const memoryUsage = process.memoryUsage();
    const systemMetrics = {
      uptime: process.uptime(),
      loadAverage: require("os").loadavg(),
      cpuUsage: process.cpuUsage(),
    };

    const recommendations = this.generatePerformanceRecommendations(
      cacheStats,
      slowOperations,
      memoryUsage,
      systemMetrics
    );

    return {
      cacheStats,
      performanceStats,
      slowOperations,
      memoryUsage,
      systemMetrics,
      recommendations,
    };
  }

  /**
   * Generate performance optimization recommendations
   */
  private static generatePerformanceRecommendations(
    cacheStats: Record<string, any>,
    slowOperations: Array<{ name: string; stats: any }>,
    memoryUsage: NodeJS.MemoryUsage,
    systemMetrics: any
  ): string[] {
    const recommendations: string[] = [];

    // Cache performance recommendations
    Object.entries(cacheStats).forEach(([cacheName, stats]) => {
      if (stats.hitRate < 0.7) {
        recommendations.push(
          `Consider increasing TTL for ${cacheName} cache (current hit rate: ${(
            stats.hitRate * 100
          ).toFixed(1)}%)`
        );
      }
      if (stats.entries > 800) {
        recommendations.push(
          `${cacheName} cache is near capacity (${stats.entries}/1000). Consider increasing maxSize or reducing TTL.`
        );
      }
    });

    // Memory usage recommendations
    const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
    if (memoryUsageMB > 100) {
      recommendations.push(
        `High memory usage detected (${memoryUsageMB.toFixed(
          1
        )}MB). Consider implementing memory optimization strategies.`
      );
    }

    // Slow operations recommendations
    slowOperations.slice(0, 3).forEach((op) => {
      const avgDuration = op.stats?.averageDuration || 0;
      recommendations.push(
        `Optimize ${op.name} operation (${avgDuration.toFixed(
          0
        )}ms average). Consider caching or async optimization.`
      );
    });

    // System load recommendations
    const avgLoad = systemMetrics.loadAverage[0];
    if (avgLoad > 2) {
      recommendations.push(
        `High system load detected (${avgLoad.toFixed(
          2
        )}). Consider reducing concurrent operations.`
      );
    }

    if (recommendations.length === 0) {
      recommendations.push(
        "Performance is optimal. No immediate optimizations needed."
      );
    }

    return recommendations;
  }

  /**
   * Get detailed cache analysis
   */
  static getCacheAnalysis(): {
    summary: {
      totalCaches: number;
      totalEntries: number;
      averageHitRate: number;
      memoryEstimate: number;
    };
    cacheDetails: Array<{
      name: string;
      hitRate: number;
      entries: number;
      memoryUsage: number;
      recommendations: string[];
    }>;
  } {
    const allStats = CacheManager.getAllStats();
    const cacheDetails: any[] = [];
    let totalEntries = 0;
    let totalHitRates = 0;
    let validCaches = 0;

    Object.entries(allStats).forEach(([name, stats]) => {
      totalEntries += stats.entries;
      if (stats.hitRate >= 0) {
        totalHitRates += stats.hitRate;
        validCaches++;
      }

      const recommendations: string[] = [];
      if (stats.hitRate < 0.5) {
        recommendations.push(
          "Consider increasing TTL or reviewing cache strategy"
        );
      }
      if (stats.entries > 800) {
        recommendations.push("Near capacity - consider increasing maxSize");
      }
      if (stats.hitRate > 0.95 && stats.entries < 100) {
        recommendations.push(
          "Excellent performance - consider increasing capacity for more data"
        );
      }

      cacheDetails.push({
        name,
        hitRate: stats.hitRate,
        entries: stats.entries,
        memoryUsage: stats.memoryUsage || 0,
        recommendations,
      });
    });

    return {
      summary: {
        totalCaches: Object.keys(allStats).length,
        totalEntries,
        averageHitRate: validCaches > 0 ? totalHitRates / validCaches : 0,
        memoryEstimate: cacheDetails.reduce(
          (sum, cache) => sum + cache.memoryUsage,
          0
        ),
      },
      cacheDetails: cacheDetails.sort((a, b) => b.hitRate - a.hitRate),
    };
  }
}
