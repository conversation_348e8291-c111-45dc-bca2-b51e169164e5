import { ServerConfig } from "../types/index.js";
import { PathManager } from "./pathManager.js";
import { SafeFileOperations } from "./safeFileOperations.js";
import { ProjectDirectoryState } from "./projectDirectoryState.js";
import { SecureCommandExecutor } from "./commandExecutor.js";
import { TimeBasedCache, CacheManager } from "./cache.js";
import { ConfigurationError } from "./errors.js";

/**
 * Service lifecycle management
 */
export enum ServiceLifetime {
  Singleton = "singleton",
  Transient = "transient",
  Scoped = "scoped",
}

/**
 * Service registration descriptor
 */
interface ServiceDescriptor<T = unknown> {
  factory: (container: ServiceContainer) => T;
  lifetime: ServiceLifetime;
  instance?: T;
  dependencies?: string[];
}

/**
 * Service registration options
 */
export interface ServiceOptions {
  lifetime?: ServiceLifetime;
  dependencies?: string[];
}

/**
 * Dependency injection container for managing services
 */
export class ServiceContainer {
  private services = new Map<string, ServiceDescriptor>();
  private singletonInstances = new Map<string, unknown>();
  private scopedInstances = new Map<string, unknown>();
  private isDisposed = false;

  /**
   * Register a service with the container
   */
  register<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: ServiceOptions = {}
  ): void {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot register services on a disposed container"
      );
    }

    const descriptor: ServiceDescriptor<T> = {
      factory,
      lifetime: options.lifetime ?? ServiceLifetime.Singleton,
      dependencies: options.dependencies ?? [],
    };

    this.services.set(name, descriptor);
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    dependencies?: string[]
  ): void {
    this.register(name, factory, {
      lifetime: ServiceLifetime.Singleton,
      dependencies,
    });
  }

  /**
   * Register a transient service
   */
  registerTransient<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    dependencies?: string[]
  ): void {
    this.register(name, factory, {
      lifetime: ServiceLifetime.Transient,
      dependencies,
    });
  }

  /**
   * Register a scoped service
   */
  registerScoped<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    dependencies?: string[]
  ): void {
    this.register(name, factory, {
      lifetime: ServiceLifetime.Scoped,
      dependencies,
    });
  }

  /**
   * Register an instance directly
   */
  registerInstance<T>(name: string, instance: T): void {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot register services on a disposed container"
      );
    }

    this.singletonInstances.set(name, instance);
    this.services.set(name, {
      factory: () => instance,
      lifetime: ServiceLifetime.Singleton,
      instance,
    });
  }

  /**
   * Resolve a service by name
   */
  resolve<T>(name: string): T {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot resolve services from a disposed container"
      );
    }

    const descriptor = this.services.get(name);
    if (!descriptor) {
      throw new ConfigurationError(`Service '${name}' is not registered`);
    }

    return this.createInstance<T>(name, descriptor);
  }

  /**
   * Try to resolve a service, returning undefined if not found
   */
  tryResolve<T>(name: string): T | undefined {
    try {
      return this.resolve<T>(name);
    } catch {
      return undefined;
    }
  }

  /**
   * Check if a service is registered
   */
  isRegistered(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all registered service names
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Create a new scope for scoped services
   */
  createScope(): ServiceContainer {
    const scope = new ServiceContainer();

    // Copy service registrations to the scope
    for (const [name, descriptor] of this.services.entries()) {
      scope.services.set(name, descriptor);
    }

    // Copy singleton instances to the scope
    for (const [name, instance] of this.singletonInstances.entries()) {
      scope.singletonInstances.set(name, instance);
    }

    return scope;
  }

  /**
   * Dispose the container and cleanup resources
   */
  dispose(): void {
    if (this.isDisposed) return;

    // Dispose services that implement IDisposable
    for (const instance of this.singletonInstances.values()) {
      if (instance && typeof (instance as any).dispose === "function") {
        try {
          (instance as any).dispose();
        } catch (error) {
          console.error("Error disposing service:", error);
        }
      }
    }

    for (const instance of this.scopedInstances.values()) {
      if (instance && typeof (instance as any).dispose === "function") {
        try {
          (instance as any).dispose();
        } catch (error) {
          console.error("Error disposing service:", error);
        }
      }
    }

    this.services.clear();
    this.singletonInstances.clear();
    this.scopedInstances.clear();
    this.isDisposed = true;
  }

  /**
   * Create an instance of a service
   */
  private createInstance<T>(name: string, descriptor: ServiceDescriptor): T {
    switch (descriptor.lifetime) {
      case ServiceLifetime.Singleton:
        return this.createSingleton<T>(name, descriptor);

      case ServiceLifetime.Scoped:
        return this.createScoped<T>(name, descriptor);

      case ServiceLifetime.Transient:
        return this.createTransient<T>(descriptor);

      default:
        throw new ConfigurationError(
          `Unknown service lifetime: ${descriptor.lifetime}`
        );
    }
  }

  /**
   * Create or get singleton instance
   */
  private createSingleton<T>(name: string, descriptor: ServiceDescriptor): T {
    if (this.singletonInstances.has(name)) {
      return this.singletonInstances.get(name) as T;
    }

    const instance = descriptor.factory(this) as T;
    this.singletonInstances.set(name, instance);
    return instance;
  }

  /**
   * Create or get scoped instance
   */
  private createScoped<T>(name: string, descriptor: ServiceDescriptor): T {
    if (this.scopedInstances.has(name)) {
      return this.scopedInstances.get(name) as T;
    }

    const instance = descriptor.factory(this) as T;
    this.scopedInstances.set(name, instance);
    return instance;
  }

  /**
   * Create transient instance
   */
  private createTransient<T>(descriptor: ServiceDescriptor): T {
    return descriptor.factory(this) as T;
  }
}

/**
 * Configure and create the default service container for the Xcode server
 */
export function createDefaultContainer(config: ServerConfig): ServiceContainer {
  const container = new ServiceContainer();

  // Register configuration
  container.registerInstance("config", config);

  // Register core services
  container.registerSingleton("pathManager", (c) => {
    const cfg = c.resolve<ServerConfig>("config");
    return new PathManager(cfg);
  });

  container.registerSingleton("fileOperations", (c) => {
    const pathManager = c.resolve<PathManager>("pathManager");
    return new SafeFileOperations(pathManager);
  });

  container.registerSingleton("directoryState", (c) => {
    const pathManager = c.resolve<PathManager>("pathManager");
    return new ProjectDirectoryState(pathManager);
  });

  container.registerSingleton("commandExecutor", () => {
    return SecureCommandExecutor;
  });

  // Register cache services
  container.registerSingleton("cacheManager", () => {
    return CacheManager;
  });

  container.registerSingleton("projectCache", (c) => {
    const cacheManager = c.resolve<typeof CacheManager>("cacheManager");
    return cacheManager.getCache("projects", {
      defaultTtl: 300000, // 5 minutes
      maxSize: 100,
    });
  });

  container.registerSingleton("fileCache", (c) => {
    const cacheManager = c.resolve<typeof CacheManager>("cacheManager");
    return cacheManager.getCache("files", {
      defaultTtl: 60000, // 1 minute
      maxSize: 500,
    });
  });

  container.registerSingleton("commandCache", (c) => {
    const cacheManager = c.resolve<typeof CacheManager>("cacheManager");
    return cacheManager.getCache("commands", {
      defaultTtl: 30000, // 30 seconds
      maxSize: 200,
    });
  });

  return container;
}

/**
 * Global service container instance
 */
let globalContainer: ServiceContainer | null = null;

/**
 * Get the global service container
 */
export function getGlobalContainer(): ServiceContainer {
  if (!globalContainer) {
    throw new ConfigurationError(
      "Global container not initialized. Call initializeGlobalContainer first."
    );
  }
  return globalContainer;
}

/**
 * Initialize the global service container
 */
export function initializeGlobalContainer(
  config: ServerConfig
): ServiceContainer {
  if (globalContainer) {
    globalContainer.dispose();
  }
  globalContainer = createDefaultContainer(config);
  return globalContainer;
}

/**
 * Dispose the global service container
 */
export function disposeGlobalContainer(): void {
  if (globalContainer) {
    globalContainer.dispose();
    globalContainer = null;
  }
}
