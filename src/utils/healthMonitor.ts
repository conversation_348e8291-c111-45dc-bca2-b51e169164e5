/**
 * Comprehensive Health Monitoring System
 * Provides real-time health checks, performance monitoring, and alerting
 */

import * as os from "os";
import { EventEmitter } from "events";
import { TimeBasedCache, CacheManager } from "./cache.js";
import { globalPerformanceMonitor } from "./performance.js";
import { PerformanceOptimizer } from "./performanceOptimizer.js";

/**
 * Health check result
 */
export interface HealthCheckResult {
  name: string;
  status: 'healthy' | 'warning' | 'critical';
  message: string;
  timestamp: Date;
  metrics?: Record<string, number>;
  details?: Record<string, unknown>;
}

/**
 * System health metrics
 */
export interface SystemHealthMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    free: number;
    total: number;
    percentage: number;
  };
  process: {
    uptime: number;
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: NodeJS.CpuUsage;
  };
  performance: {
    slowOperations: number;
    averageResponseTime: number;
    errorRate: number;
  };
  cache: {
    hitRate: number;
    totalEntries: number;
    memoryUsage: number;
  };
}

/**
 * Health check configuration
 */
export interface HealthCheckConfig {
  interval: number; // milliseconds
  thresholds: {
    cpu: number;
    memory: number;
    responseTime: number;
    errorRate: number;
    cacheHitRate: number;
  };
  alerts: {
    enabled: boolean;
    cooldown: number; // milliseconds
  };
}

/**
 * Health monitoring system
 */
export class HealthMonitor extends EventEmitter {
  private static instance: HealthMonitor;
  private checks: Map<string, () => Promise<HealthCheckResult>> = new Map();
  private lastResults: Map<string, HealthCheckResult> = new Map();
  private config: HealthCheckConfig;
  private monitoringInterval?: NodeJS.Timeout;
  private alertCooldowns: Map<string, number> = new Map();
  private startTime: Date = new Date();

  private constructor(config: Partial<HealthCheckConfig> = {}) {
    super();
    this.config = {
      interval: 30000, // 30 seconds
      thresholds: {
        cpu: 80, // 80%
        memory: 85, // 85%
        responseTime: 1000, // 1 second
        errorRate: 5, // 5%
        cacheHitRate: 70, // 70%
      },
      alerts: {
        enabled: true,
        cooldown: 300000, // 5 minutes
      },
      ...config,
    };

    this.registerDefaultChecks();
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<HealthCheckConfig>): HealthMonitor {
    if (!HealthMonitor.instance) {
      HealthMonitor.instance = new HealthMonitor(config);
    }
    return HealthMonitor.instance;
  }

  /**
   * Register default health checks
   */
  private registerDefaultChecks(): void {
    this.registerCheck('system_resources', this.checkSystemResources.bind(this));
    this.registerCheck('memory_usage', this.checkMemoryUsage.bind(this));
    this.registerCheck('performance', this.checkPerformance.bind(this));
    this.registerCheck('cache_health', this.checkCacheHealth.bind(this));
    this.registerCheck('error_rate', this.checkErrorRate.bind(this));
  }

  /**
   * Register a custom health check
   */
  registerCheck(name: string, check: () => Promise<HealthCheckResult>): void {
    this.checks.set(name, check);
  }

  /**
   * Start health monitoring
   */
  start(): void {
    if (this.monitoringInterval) {
      return; // Already started
    }

    this.monitoringInterval = setInterval(async () => {
      await this.runAllChecks();
    }, this.config.interval);

    // Run initial check
    this.runAllChecks();
  }

  /**
   * Stop health monitoring
   */
  stop(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }

  /**
   * Run all registered health checks
   */
  async runAllChecks(): Promise<Map<string, HealthCheckResult>> {
    const results = new Map<string, HealthCheckResult>();

    for (const [name, check] of this.checks) {
      try {
        const result = await check();
        results.set(name, result);
        this.lastResults.set(name, result);

        // Emit events for status changes
        this.emit('health_check', result);
        
        if (result.status === 'critical' || result.status === 'warning') {
          this.handleAlert(result);
        }
      } catch (error) {
        const errorResult: HealthCheckResult = {
          name,
          status: 'critical',
          message: `Health check failed: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date(),
        };
        results.set(name, errorResult);
        this.lastResults.set(name, errorResult);
        this.emit('health_check_error', errorResult);
      }
    }

    return results;
  }

  /**
   * Get current health status
   */
  getHealthStatus(): {
    overall: 'healthy' | 'warning' | 'critical';
    checks: HealthCheckResult[];
    uptime: number;
    lastCheck: Date;
  } {
    const checks = Array.from(this.lastResults.values());
    let overall: 'healthy' | 'warning' | 'critical' = 'healthy';

    // Determine overall status
    if (checks.some(check => check.status === 'critical')) {
      overall = 'critical';
    } else if (checks.some(check => check.status === 'warning')) {
      overall = 'warning';
    }

    return {
      overall,
      checks,
      uptime: Date.now() - this.startTime.getTime(),
      lastCheck: new Date(),
    };
  }

  /**
   * Get comprehensive system metrics
   */
  getSystemMetrics(): SystemHealthMetrics {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const loadAverage = os.loadavg();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    const performanceStats = PerformanceOptimizer.getPerformanceStats();
    const cacheStats = CacheManager.getAllStats();

    // Calculate cache metrics
    let totalCacheEntries = 0;
    let totalCacheHits = 0;
    let totalCacheRequests = 0;
    let totalCacheMemory = 0;

    Object.values(cacheStats).forEach(stats => {
      totalCacheEntries += stats.entries;
      totalCacheHits += stats.hits;
      totalCacheRequests += stats.hits + stats.misses;
      totalCacheMemory += stats.memoryUsage || 0;
    });

    const cacheHitRate = totalCacheRequests > 0 ? totalCacheHits / totalCacheRequests : 0;

    return {
      cpu: {
        usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
        loadAverage,
      },
      memory: {
        used: usedMemory,
        free: freeMemory,
        total: totalMemory,
        percentage: (usedMemory / totalMemory) * 100,
      },
      process: {
        uptime: process.uptime(),
        memoryUsage,
        cpuUsage,
      },
      performance: {
        slowOperations: performanceStats.slowOperations.length,
        averageResponseTime: this.calculateAverageResponseTime(performanceStats.performanceStats),
        errorRate: 0, // TODO: Implement error tracking
      },
      cache: {
        hitRate: cacheHitRate,
        totalEntries: totalCacheEntries,
        memoryUsage: totalCacheMemory,
      },
    };
  }

  /**
   * Handle alert notifications
   */
  private handleAlert(result: HealthCheckResult): void {
    if (!this.config.alerts.enabled) {
      return;
    }

    const now = Date.now();
    const lastAlert = this.alertCooldowns.get(result.name) || 0;

    if (now - lastAlert < this.config.alerts.cooldown) {
      return; // Still in cooldown
    }

    this.alertCooldowns.set(result.name, now);
    this.emit('alert', result);
  }

  /**
   * System resources health check
   */
  private async checkSystemResources(): Promise<HealthCheckResult> {
    const loadAverage = os.loadavg();
    const cpuCount = os.cpus().length;
    const load1min = loadAverage[0];
    const loadPercentage = (load1min / cpuCount) * 100;

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    let message = `System load: ${loadPercentage.toFixed(1)}%`;

    if (loadPercentage > this.config.thresholds.cpu) {
      status = 'critical';
      message += ' - High CPU load detected';
    } else if (loadPercentage > this.config.thresholds.cpu * 0.8) {
      status = 'warning';
      message += ' - Elevated CPU load';
    }

    return {
      name: 'system_resources',
      status,
      message,
      timestamp: new Date(),
      metrics: {
        loadPercentage,
        load1min,
        cpuCount,
      },
    };
  }

  /**
   * Memory usage health check
   */
  private async checkMemoryUsage(): Promise<HealthCheckResult> {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryPercentage = (usedMemory / totalMemory) * 100;

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    let message = `Memory usage: ${memoryPercentage.toFixed(1)}%`;

    if (memoryPercentage > this.config.thresholds.memory) {
      status = 'critical';
      message += ' - High memory usage detected';
    } else if (memoryPercentage > this.config.thresholds.memory * 0.8) {
      status = 'warning';
      message += ' - Elevated memory usage';
    }

    return {
      name: 'memory_usage',
      status,
      message,
      timestamp: new Date(),
      metrics: {
        memoryPercentage,
        usedMemory,
        freeMemory,
        totalMemory,
      },
    };
  }

  /**
   * Performance health check
   */
  private async checkPerformance(): Promise<HealthCheckResult> {
    const stats = PerformanceOptimizer.getPerformanceStats();
    const slowOperations = stats.slowOperations.length;
    const avgResponseTime = this.calculateAverageResponseTime(stats.performanceStats);

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    let message = `Average response time: ${avgResponseTime.toFixed(0)}ms`;

    if (avgResponseTime > this.config.thresholds.responseTime) {
      status = 'critical';
      message += ' - Slow response times detected';
    } else if (avgResponseTime > this.config.thresholds.responseTime * 0.8) {
      status = 'warning';
      message += ' - Elevated response times';
    }

    if (slowOperations > 5) {
      status = 'warning';
      message += `, ${slowOperations} slow operations`;
    }

    return {
      name: 'performance',
      status,
      message,
      timestamp: new Date(),
      metrics: {
        avgResponseTime,
        slowOperations,
      },
    };
  }

  /**
   * Cache health check
   */
  private async checkCacheHealth(): Promise<HealthCheckResult> {
    const cacheStats = CacheManager.getAllStats();
    let totalHits = 0;
    let totalRequests = 0;

    Object.values(cacheStats).forEach(stats => {
      totalHits += stats.hits;
      totalRequests += stats.hits + stats.misses;
    });

    const hitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 100;

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    let message = `Cache hit rate: ${hitRate.toFixed(1)}%`;

    if (hitRate < this.config.thresholds.cacheHitRate) {
      status = 'warning';
      message += ' - Low cache hit rate';
    }

    return {
      name: 'cache_health',
      status,
      message,
      timestamp: new Date(),
      metrics: {
        hitRate,
        totalHits,
        totalRequests,
      },
    };
  }

  /**
   * Error rate health check
   */
  private async checkErrorRate(): Promise<HealthCheckResult> {
    // TODO: Implement error tracking
    return {
      name: 'error_rate',
      status: 'healthy',
      message: 'Error rate monitoring not implemented',
      timestamp: new Date(),
    };
  }

  /**
   * Calculate average response time from performance stats
   */
  private calculateAverageResponseTime(performanceStats: Record<string, any>): number {
    const operations = Object.values(performanceStats);
    if (operations.length === 0) return 0;

    const totalTime = operations.reduce((sum: number, op: any) => sum + (op.averageDuration || 0), 0);
    return totalTime / operations.length;
  }
}

/**
 * Global health monitor instance
 */
export const globalHealthMonitor = HealthMonitor.getInstance();
